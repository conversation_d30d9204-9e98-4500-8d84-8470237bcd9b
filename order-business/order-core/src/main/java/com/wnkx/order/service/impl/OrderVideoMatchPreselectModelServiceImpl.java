package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.BusinessConstants;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.CheckedException;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.ModelListDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.ModelPerson;
import com.ruoyi.system.api.domain.entity.OrderVideoModel;
import com.ruoyi.system.api.domain.entity.SysUser;
import com.ruoyi.system.api.domain.entity.order.*;
import com.ruoyi.system.api.domain.vo.biz.business.user.UserBlackModelVO;
import com.ruoyi.system.api.domain.vo.biz.model.AddPreselectModelListVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.wnkx.order.config.OrderVideoProperties;
import com.wnkx.order.mapper.OrderVideoMatchPreselectModelMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单_视频_预选模特Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderVideoMatchPreselectModelServiceImpl extends ServiceImpl<OrderVideoMatchPreselectModelMapper, OrderVideoMatchPreselectModel> implements IOrderVideoMatchPreselectModelService {

    private final RemoteService remoteService;
    private final IOrderVideoModelService orderVideoModelService;
    private final OrderVideoProperties orderVideoProperties;
    private final IOrderService orderService;
    private final RedisService redisService;
    private final OrderResourceService orderResourceService;
    private final IOrderVideoContentService videoContentService;

    /**
     * 设置已提醒模特拍摄注意事项
     */
    @Override
    public void setModelShootAttentionRemind(Long preselectModelId) {
        baseMapper.setModelShootAttentionRemind(preselectModelId);
    }

    @Override
    public List<UserVO> distributionIssueList() {
        return baseMapper.distributionIssueList();
    }

    /**
     * 我的预选-分发中-英文部客服下拉框
     */
    @Override
    public List<UserVO> myPreselectDistributionEnglishSelect(String keyword) {
        List<Long> modelIds = baseMapper.myPreselectDistributionModelSelect();
        if (CollUtil.isEmpty(modelIds)) {
            return Collections.emptyList();
        }

        List<ModelPerson> modelPeople = remoteService.queryModelPerson(modelIds);
        if (CollUtil.isEmpty(modelPeople)) {
            return Collections.emptyList();
        }

        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(modelPeople.stream().map(ModelPerson::getUserId).collect(Collectors.toSet()));
        dto.setUserName(keyword);
        List<SysUser> userList = remoteService.getUserList(dto);

        List<UserVO> userVOS = new ArrayList<>();

        userList.forEach(user -> {
            UserVO userVO = new UserVO();
            userVO.setId(user.getUserId());
            userVO.setName(user.getUserName());
            userVO.setPhonenumber(user.getPhonenumber());
            userVOS.add(userVO);

        });

        return userVOS;
    }

    /**
     * 我的预选-分发中-分发模特下拉框
     */
    @Override
    public List<ModelInfoVO> myPreselectDistributionModelSelect(String keyword) {
        List<Long> modelIds = baseMapper.myPreselectDistributionModelSelect();
        if (CollUtil.isEmpty(modelIds)) {
            return Collections.emptyList();
        }

        ModelListDTO modelListDTO = new ModelListDTO();
        modelListDTO.setId(modelIds);
        modelListDTO.setName(keyword);

        return remoteService.innerList(modelListDTO);
    }

    /**
     * 预选管理-设置模特分发结果
     */
    @Override
    public void setModelDistributionResult(SetModelDistributionResultDTO dto) {
        OrderVideoMatchPreselectModel orderVideoMatchPreselectModel = baseMapper.selectById(dto.getPreselectModelId());
        Assert.notNull(orderVideoMatchPreselectModel, "预选模特不存在");
        Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatchPreselectModel.getMatchId(), CacheConstants.ORDER_MATCH_LOCK_KEY_SECOND), "系统繁忙，请稍后重试~");

        try {
            Assert.isTrue(DistributionResultEnum.PENDING.getCode().equals(orderVideoMatchPreselectModel.getDistributionResult()), "订单分发已结束，请刷新页面");
            Assert.isTrue(PreselectModelAddTypeEnum.DISTRIBUTION.getCode().equals(orderVideoMatchPreselectModel.getAddType()), "不是分发的模特");
            Assert.isTrue(PreselectStatusEnum.UN_JOINTED.getCode().equals(orderVideoMatchPreselectModel.getStatus()) || PreselectStatusEnum.JOINTED.getCode().equals(orderVideoMatchPreselectModel.getStatus()), "预选模特状态错误");
            Assert.isTrue(ModelIntentionEnum.MT_UN_CONFIRM.getCode().equals(orderVideoMatchPreselectModel.getModelIntention()), "模特意向状态错误");

            if (!SecurityUtils.currentUserIsAdmin()) {
                List<ModelPerson> modelPeople = remoteService.selectCurrentUserRelevanceModel();
                Assert.isTrue(modelPeople.stream().anyMatch(item -> item.getModelId().equals(orderVideoMatchPreselectModel.getModelId())), "仅可操作自己关联的模特");
            }

            DateTime dateTime = DateUtil.date();
            if (DistributionResultEnum.WANT.getCode().equals(dto.getDistributionResult())) {
                orderVideoMatchPreselectModel.setStatus(PreselectStatusEnum.UN_JOINTED.getCode());
                orderVideoMatchPreselectModel.setModelIntention(ModelIntentionEnum.WANT.getCode());
                orderVideoMatchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.IN_REVIEW.getCode());
                orderVideoMatchPreselectModel.setSelectTime(dateTime);
            } else if (DistributionResultEnum.WANT_NOT.getCode().equals(dto.getDistributionResult())) {
                orderVideoMatchPreselectModel.setModelIntention(ModelIntentionEnum.WANT_NOT.getCode());
                orderVideoMatchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.REJECT.getCode());
                orderVideoMatchPreselectModel.setSelectTime(dateTime);
            } else if (DistributionResultEnum.CANCEL_DISTRIBUTION.getCode().equals(dto.getDistributionResult())) {
                orderVideoMatchPreselectModel.setDistributionResultCause(DistributionResultCauseEnum.CUSTOMER_SERVICE_CANCELLATION.getCode());
            } else {
                throw new ServiceException("[分发结果]输入错误");
            }
            orderVideoMatchPreselectModel.setDistributionResult(dto.getDistributionResult());
            orderVideoMatchPreselectModel.setDistributionResultTime(dateTime);
            baseMapper.updateById(orderVideoMatchPreselectModel);
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatchPreselectModel.getMatchId());
        }
    }

    /**
     * 预选管理-批量标记沟通
     */
    @Override
    public void batchMarkCommunication(List<Long> preselectModelIds) {
        List<OrderVideoMatchPreselectModel> orderVideoMatchPreselectModels = baseMapper.selectBatchIds(preselectModelIds);
        Assert.notNull(orderVideoMatchPreselectModels, "预选模特不存在");
        Assert.notEmpty(orderVideoMatchPreselectModels, "预选模特不存在");
        Assert.isTrue(orderVideoMatchPreselectModels.size() == preselectModelIds.size(), "预选模特不存在");

        List<Long> lockIds = new ArrayList<>();
        try {
            for (OrderVideoMatchPreselectModel orderVideoMatchPreselectModel : orderVideoMatchPreselectModels) {
                Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatchPreselectModel.getMatchId(), CacheConstants.ORDER_MATCH_LOCK_KEY_SECOND), "系统繁忙，请稍后重试~");
                lockIds.add(orderVideoMatchPreselectModel.getMatchId());
            }
        } catch (Exception e) {
            for (Long lockId : lockIds) {
                redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + lockId);
            }
        }

        try {
            if (!SecurityUtils.currentUserIsAdmin()) {
                List<ModelPerson> modelPeople = remoteService.selectCurrentUserRelevanceModel();
                List<Long> modelIds = modelPeople.stream().map(ModelPerson::getModelId).collect(Collectors.toList());
                Assert.isTrue(CollUtil.containsAll(modelIds, orderVideoMatchPreselectModels.stream().map(OrderVideoMatchPreselectModel::getModelId).collect(Collectors.toList())), "仅可操作自己关联的模特");
            }

            List<OrderVideoMatch> orderVideoMatches = SpringUtils.getBean(OrderVideoMatchService.class).listByIds(orderVideoMatchPreselectModels.stream().map(OrderVideoMatchPreselectModel::getMatchId).collect(Collectors.toList()));
            List<OrderVideo> orderVideos = SpringUtils.getBean(IOrderVideoService.class).listByIds(orderVideoMatches.stream().map(OrderVideoMatch::getVideoId).collect(Collectors.toList()));
            SpringUtils.getBean(IOrderVideoService.class).checkVideoStatus(orderVideos, OrderStatusEnum.UN_MATCH);

            for (OrderVideoMatchPreselectModel orderVideoMatchPreselectModel : orderVideoMatchPreselectModels) {
                if (!OrderVideoModelSelectStatusEnum.UN_HANDLE.getCode().equals(orderVideoMatchPreselectModel.getSelectStatus()) && !OrderVideoModelSelectStatusEnum.IN_REVIEW.getCode().equals(orderVideoMatchPreselectModel.getSelectStatus())) {
                    continue;
                }

                if (!orderVideoMatchPreselectModel.getStatus().equals(PreselectStatusEnum.UN_JOINTED.getCode())) {
                    continue;
                }

                if (ModelIntentionEnum.UN_CONFIRM.getCode().equals(orderVideoMatchPreselectModel.getModelIntention())) {
                    orderVideoMatchPreselectModel.setModelIntention(ModelIntentionEnum.MT_UN_CONFIRM.getCode());
                    orderVideoMatchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.UN_HANDLE.getCode());
                }

                orderVideoMatchPreselectModel.setStatus(PreselectStatusEnum.JOINTED.getCode());
            }

            baseMapper.updateBatchById(orderVideoMatchPreselectModels);
        } finally {
            for (Long lockId : lockIds) {
                redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + lockId);
            }
        }
    }

    /**
     * 预选管理-我的预选-历史分发记录
     */
    @Override
    public List<DistributionHistoryListVO> selectDistributionHistoryListByModelId(Long modelId) {
        return baseMapper.selectDistributionHistoryListByModelId(modelId);
    }

    /**
     * 预选管理-我的预选-分发中
     */
    @Override
    public List<MyPreselectDistributionListVO> selectMyPreselectDistributionListByCondition(MyPreselectDistributionListDTO dto) {
        if (CollUtil.isNotEmpty(dto.getAddPreselectTimes())) {
            Map<String, String> addPreselectTimeMap = new HashMap<>();
            dto.getAddPreselectTimes().forEach(data -> {
                if (OrderPoolPreselectedTimeEnum.WITHIN_24_HOURS.getCode().equals(data)) {
                    addPreselectTimeMap.put(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -1), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN));
                } else if (OrderPoolPreselectedTimeEnum.ONE_TO_TWO_DAYS.getCode().equals(data)) {
                    addPreselectTimeMap.put(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -2), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -1), DatePattern.NORM_DATETIME_PATTERN));
                } else if (OrderPoolPreselectedTimeEnum.TWO_TO_THREE_DAYS.getCode().equals(data)) {
                    addPreselectTimeMap.put(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -3), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -2), DatePattern.NORM_DATETIME_PATTERN));
                } else if (OrderPoolPreselectedTimeEnum.MORE_THAN_THREE_DAYS.getCode().equals(data)) {
                    addPreselectTimeMap.put(DateUtil.format(DateUtil.offset(DateUtil.date(), DateField.YEAR, -1), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -3), DatePattern.NORM_DATETIME_PATTERN));
                }
            });
            dto.setAddPreselectTimeMap(addPreselectTimeMap);
        }
        if (CollUtil.isNotEmpty(dto.getEnglishCustomerServiceIds())) {
            List<ModelPerson> modelPeople = remoteService.selectBackUserRelevanceModelByUserIds(dto.getEnglishCustomerServiceIds());
            dto.getModelIds().addAll(modelPeople.stream().map(ModelPerson::getModelId).collect(Collectors.toList()));
        }

        PageUtils.startPage();
        List<MyPreselectDistributionListVO> listVOS = baseMapper.selectMyPreselectDistributionModelListByCondition(dto);
        if (CollUtil.isEmpty(listVOS)) {
            return listVOS;
        }

        //  获取模特ID 以及 预选模特ID
        List<Long> modelIds = new ArrayList<>();
        List<Long> ovmpmIds = new ArrayList<>();
        for (MyPreselectDistributionListVO listVO : listVOS) {
            modelIds.add(listVO.getModelId());
            ovmpmIds.addAll(StringUtils.splitToLong(listVO.getOvmpmIds(), StrPool.COMMA));
        }

        List<DistributionOrderListVO> distributionOrderListVOS = baseMapper.selectMyPreselectDistributionOrderListByOvmpmIds(ovmpmIds);
        Map<Long, List<DistributionOrderListVO>> distributionOrderListVOMap = distributionOrderListVOS.stream().collect(Collectors.groupingBy(DistributionOrderListVO::getModelId));

        List<Long> videoIds = distributionOrderListVOS.stream().map(DistributionOrderListVO::getVideoId).filter(Objects::nonNull).collect(Collectors.toList());

        //  查询参考图资源
        List<String> referencePicIds = distributionOrderListVOS.stream().map(DistributionOrderListVO::getReferencePicId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> resourceIds = new ArrayList<>(StringUtils.splitToLong(referencePicIds, StrUtil.COMMA));

        //  查询视频订单注意事项的图片
        List<OrderVideo> orderVideos = SpringUtils.getBean(IOrderVideoService.class).listByIds(videoIds);
        List<String> cautionsPicIds = orderVideos.stream().map(OrderVideo::getCautionsPicId).filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
        List<String> particularEmphasisPicIds = orderVideos.stream().map(OrderVideo::getParticularEmphasisPicIds).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(cautionsPicIds)) {
            resourceIds.addAll(StringUtils.splitToLong(cautionsPicIds, StrUtil.COMMA));
        }
        if (CollUtil.isNotEmpty(particularEmphasisPicIds)) {
            resourceIds.addAll(StringUtils.splitToLong(particularEmphasisPicIds, StrUtil.COMMA));
        }
        Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(resourceIds);
        Map<Long, OrderVideo> orderVideoMap = orderVideos.stream().collect(Collectors.toMap(OrderVideo::getId, Function.identity()));

        //  查询拍摄要求、注意事项
        List<OrderVideoContent> orderVideoContents = videoContentService.selectListByVideoIds(videoIds);
        Map<Long, List<OrderVideoContent>> orderVideoContentMap = orderVideoContents.stream().collect(Collectors.groupingBy(OrderVideoContent::getVideoId));

        //  查询对接人（中文部客服
        List<Long> contactIds = distributionOrderListVOS.stream().map(DistributionOrderListVO::getContactId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(contactIds).build());

        //  查询模特信息
        List<AddPreselectModelListVO> modelListVOS = remoteService.selectModelInfoOfPreselection(ModelListDTO.builder().id(modelIds).build());
        Map<Long, AddPreselectModelListVO> modelListVOMap = modelListVOS.stream().collect(Collectors.toMap(AddPreselectModelListVO::getId, Function.identity()));

        //  查询模特携带数
        List<ModelOrderVO> modelOrderCount = orderService.getModelOrderCount(modelIds);
        Map<Long, ModelOrderVO> modelOrderVOMap = modelOrderCount.stream().collect(Collectors.toMap(ModelOrderVO::getModelId, Function.identity()));

        List<HistoryPreselectModelCountVO> historyPreselectModelCountList = SpringUtils.getBean(OrderVideoMatchService.class).getHistoryPreselectModelCountList(videoIds);
        Map<Long, Integer> historyPreselectModelCountMap = historyPreselectModelCountList.stream().collect(Collectors.toMap(HistoryPreselectModelCountVO::getVideoId, HistoryPreselectModelCountVO::getHistoryPreselectModelCount));

        for (MyPreselectDistributionListVO listVO : listVOS) {
            AddPreselectModelListVO addPreselectModelListVO = modelListVOMap.get(listVO.getModelId());
            listVO.setModelPic(addPreselectModelListVO.getModelPic());
            listVO.setName(addPreselectModelListVO.getName());
            listVO.setAccount(addPreselectModelListVO.getAccount());
            listVO.setType(addPreselectModelListVO.getType());
            listVO.setCooperation(addPreselectModelListVO.getCooperation());
            listVO.setCooperationScore(addPreselectModelListVO.getCooperationScore());
            listVO.setCommissionUnit(addPreselectModelListVO.getCommissionUnit());
            listVO.setCommission(addPreselectModelListVO.getCommission());
            listVO.setAfterSaleRate(addPreselectModelListVO.getAfterSaleRate());
            listVO.setEnglishServiceName(addPreselectModelListVO.getPersons().get(0).getName());

            ModelOrderVO modelOrderVO = modelOrderVOMap.getOrDefault(listVO.getModelId(), new ModelOrderVO());
            listVO.setWaits(modelOrderVO.getWaits());
            listVO.setCarryCount(modelOrderVO.getCarryCount());

            List<DistributionOrderListVO> orderListVOS = distributionOrderListVOMap.get(listVO.getModelId());
            for (DistributionOrderListVO orderListVO : orderListVOS) {
                //  参考图片
                List<Long> referencePicLongIds = StringUtils.splitToLong(orderListVO.getReferencePicId(), StrUtil.COMMA);
                for (Long referencePicId : referencePicLongIds) {
                    orderListVO.getReferencePic().add(resourceMap.getOrDefault(referencePicId, new OrderResource()).getObjectKey());
                }

                //  剩余退款照片数量
                if (ObjectUtil.isNotNull(orderListVO.getPicCount())) {
                    orderListVO.setSurplusPicCount(PicCountEnum.getValue(orderListVO.getPicCount()) - Optional.ofNullable(orderListVO.getRefundPicCount()).orElse(0));
                } else {
                    orderListVO.setSurplusPicCount(0);
                }

                //  拍摄建议/模特要求/产品卖点
                List<OrderVideoContent> videoContents = orderVideoContentMap.get(orderListVO.getVideoId());
                if (CollUtil.isNotEmpty(videoContents)) {
                    List<OrderVideoContent> shootRequired = videoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.REQUIRE.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
                    List<OrderVideoContent> cautions = videoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.CAUTIONS.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
                    List<OrderVideoContent> orderSpecificationRequire = videoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.ORDER_SPECIFICATION_REQUIRE.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
                    List<OrderVideoContent> particularEmphasis = videoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.PARTICULAR_EMPHASIS.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
                    List<OrderVideoContent> sellingPointProducts = videoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.SELLING_POINT_PRODUCT.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());

                    OrderVideoCautionsVO orderVideoCautionsVO = new OrderVideoCautionsVO();
                    OrderVideo orderVideo = orderVideoMap.get(orderListVO.getVideoId());
                    if (CharSequenceUtil.isNotBlank(orderVideo.getCautionsPicId())) {
                        for (Long cautionsPicId : StringUtils.splitToLong(orderVideo.getCautionsPicId(), StrUtil.COMMA)) {
                            orderVideoCautionsVO.getCautionsPics().add(resourceMap.getOrDefault(cautionsPicId, new OrderResource()).getObjectKey());
                        }
                    }
                    List<String> particularEmphasisPic = new ArrayList<>();
                    if (CharSequenceUtil.isNotBlank(orderVideo.getParticularEmphasisPicIds())) {
                        for (Long particularEmphasisPicId : StringUtils.splitToLong(orderVideo.getParticularEmphasisPicIds(), StrUtil.COMMA)) {
                            particularEmphasisPic.add(resourceMap.getOrDefault(particularEmphasisPicId, new OrderResource()).getObjectKey());
                        }
                    }
                    orderVideoCautionsVO.setCautions(cautions);
                    orderListVO.setShootRequired(shootRequired);
                    orderListVO.setOrderVideoCautionsVO(orderVideoCautionsVO);
                    orderListVO.setOrderSpecificationRequire(CollUtil.isNotEmpty(orderSpecificationRequire) ? orderSpecificationRequire.get(0).getContent() : null);
                    orderListVO.setParticularEmphasis(CollUtil.isNotEmpty(particularEmphasis) ? particularEmphasis.get(0).getContent() : null);
                    orderListVO.setParticularEmphasisPic(particularEmphasisPic);
                    orderListVO.setSellingPointProduct(CollUtil.isNotEmpty(sellingPointProducts) ? sellingPointProducts.get(0).getContent() : null);
                }
                orderListVO.setChineseServiceName(userMap.getOrDefault(orderListVO.getContactId(), new UserVO()).getName());
                orderListVO.setHistoryPreselectModelCount(historyPreselectModelCountMap.getOrDefault(orderListVO.getVideoId(), 0));
            }
            orderListVOS.sort(Comparator.comparing(DistributionOrderListVO::getAddTime).reversed());

            listVO.setDistributionOrderListVOS(orderListVOS);
        }
        return listVOS;
    }

    /**
     * 添加分发模特列表查询匹配单下的模特
     */
    @Override
    public Set<Long> selectPreselectModelIdsByMatchId(Long matchId) {
        return baseMapper.selectListByMatchId(matchId).stream()
                .filter(item -> ObjectUtil.isNull(item.getDistributionResult()) || !DistributionResultEnum.CANCEL_DISTRIBUTION.getCode().equals(item.getDistributionResult()))
                .map(OrderVideoMatchPreselectModel::getModelId).collect(Collectors.toSet());
    }

    /**
     * 预选管理-添加分发
     */
    @Override
    public AddDistributionErrorVO addDistribution(AddDistributionDTO dto, OrderVideo orderVideo) {
        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(dto.getModelIds());
        Assert.notNull(modelMap, "模特不存在");
        Assert.notEmpty(modelMap, "模特不存在");
        Assert.isTrue(modelMap.size() == dto.getModelIds().size(), "模特不存在");
        Assert.isTrue(modelMap.values().stream().allMatch(model -> ModelStatusEnum.NORMAL.getCode().equals(model.getStatus())), "列表中存在不满足接单的模特，请检查重试");

        AddDistributionErrorVO addDistributionErrorVO = new AddDistributionErrorVO();

        List<Long> changeModelIds = checkModelConformOrderVideoInfoReturnModelId(modelMap, orderVideo);
        List<Long> addModelIds = new ArrayList<>();
        List<Long> intentionModelIds = new ArrayList<>();

        List<OrderVideoMatchPreselectModel> orderVideoMatchPreselectModels = baseMapper.selectListByMatchId(dto.getMatchId());
        Map<Long, List<OrderVideoMatchPreselectModel>> orderVideoMatchPreselectModelMap = orderVideoMatchPreselectModels.stream().collect(Collectors.groupingBy(OrderVideoMatchPreselectModel::getModelId));

        List<OrderVideoMatchPreselectModel> saveOrderVideoMatchPreselectModels = new ArrayList<>();

        Long userId = SecurityUtils.getUserId();
        DateTime dateTime = DateUtil.date();

        for (Long modelId : dto.getModelIds()) {
            List<OrderVideoMatchPreselectModel> orderVideoMatchPreselectModel = orderVideoMatchPreselectModelMap.get(modelId);
            if (CollUtil.isNotEmpty(orderVideoMatchPreselectModel)) {
                if (orderVideoMatchPreselectModel.stream().anyMatch(item -> PreselectModelAddTypeEnum.INTENTION_MODEL.getCode().equals(item.getAddType())
                        && !PreselectStatusEnum.OUT.getCode().equals(item.getStatus()))) {
                    intentionModelIds.add(modelId);
                } else if (orderVideoMatchPreselectModel.stream().anyMatch(item -> !PreselectModelAddTypeEnum.DISTRIBUTION.getCode().equals(item.getAddType())
                        || !DistributionResultEnum.CANCEL_DISTRIBUTION.getCode().equals(item.getDistributionResult()))) {
                    addModelIds.add(modelId);
                }
            }

            OrderVideoMatchPreselectModel saveOrderVideoMatchPreselectModel = new OrderVideoMatchPreselectModel();
            saveOrderVideoMatchPreselectModel.setMatchId(dto.getMatchId());
            saveOrderVideoMatchPreselectModel.setModelId(modelId);
            saveOrderVideoMatchPreselectModel.setAddType(PreselectModelAddTypeEnum.DISTRIBUTION.getCode());
            saveOrderVideoMatchPreselectModel.setAddUserId(userId);
            saveOrderVideoMatchPreselectModel.setAddTime(dateTime);
            saveOrderVideoMatchPreselectModel.setDistributionResult(DistributionResultEnum.PENDING.getCode());
            saveOrderVideoMatchPreselectModel.setDistributionResultTime(dateTime);

            saveOrderVideoMatchPreselectModels.add(saveOrderVideoMatchPreselectModel);
        }

        if (CollUtil.isEmpty(changeModelIds)
                && CollUtil.isEmpty(addModelIds)
                && CollUtil.isEmpty(intentionModelIds)
        ) {
            baseMapper.saveBatch(saveOrderVideoMatchPreselectModels);
        } else {
            addDistributionErrorVO.setAddModelIds(addModelIds);
            addDistributionErrorVO.setIntentionModelIds(intentionModelIds);
            addDistributionErrorVO.setChangeModelIds(changeModelIds);
        }

        return addDistributionErrorVO;
    }

    /**
     * 预选管理-填写模特拍摄注意事项
     */
    @Override
    public void preselectModelShootAttention(PreselectModelShootAttentionDTO dto) {
        OrderVideoMatchPreselectModel orderVideoMatchPreselectModel = baseMapper.selectById(dto.getPreselectModelId());
        Assert.notNull(orderVideoMatchPreselectModel, "预选模特不存在");
        Assert.isTrue(!PreselectStatusEnum.OUT.getCode().equals(orderVideoMatchPreselectModel.getStatus()), "模特已淘汰，无需编辑");
        if (!SecurityUtils.currentUserIsAdmin()) {
            List<ModelPerson> modelPeople = remoteService.selectCurrentUserRelevanceModel();
            Assert.isTrue(CollUtil.isNotEmpty(modelPeople) && new HashSet<>(modelPeople.stream().map(ModelPerson::getModelId).collect(Collectors.toList())).contains(orderVideoMatchPreselectModel.getModelId()), "仅可操作自己关联的模特");
        }

        if (CharSequenceUtil.isNotBlank(dto.getShootAttention())
                && ObjectUtil.notEqual(orderVideoMatchPreselectModel.getShootAttention(), dto.getShootAttention())
                && ObjectUtil.isNull(orderVideoMatchPreselectModel.getNeedRemindShootAttention())
        ) {
            orderVideoMatchPreselectModel.setNeedRemindShootAttention(StatusTypeEnum.YES.getCode());
        } else if (CharSequenceUtil.isBlank(dto.getShootAttention())) {
            orderVideoMatchPreselectModel.setNeedRemindShootAttention(StatusTypeEnum.NO.getCode());
        }
        orderVideoMatchPreselectModel.setShootAttention(dto.getShootAttention() == null ? CharSequenceUtil.EMPTY : dto.getShootAttention());
        if (CollUtil.isNotEmpty(dto.getShootAttentionObjectKey())) {
            orderVideoMatchPreselectModel.setShootAttentionObjectKey(String.join(",", dto.getShootAttentionObjectKey()));
        } else {
            orderVideoMatchPreselectModel.setShootAttentionObjectKey(null);
        }

        baseMapper.updateById(orderVideoMatchPreselectModel);
    }

    /**
     * 预选管理-查询预选模特拍摄注意事项
     */
    @Override
    public PreselectModelShootAttentionVO getPreselectModelShootAttention(Long preselectModelId) {
        OrderVideoMatchPreselectModel orderVideoMatchPreselectModel = baseMapper.selectById(preselectModelId);
        Assert.notNull(orderVideoMatchPreselectModel, "预选模特不存在");
        PreselectModelShootAttentionVO vo = new PreselectModelShootAttentionVO();
        vo.setShootAttention(orderVideoMatchPreselectModel.getShootAttention());
        if (CharSequenceUtil.isNotBlank(orderVideoMatchPreselectModel.getShootAttentionObjectKey())) {
            vo.setShootAttentionObjectKey(Arrays.asList(orderVideoMatchPreselectModel.getShootAttentionObjectKey().split(",")));
        }
        return vo;
    }

    /**
     * 我的预选-沟通中-预选模特下拉框
     */
    @Override
    public List<ModelInfoVO> myPreselectDockingModelSelect(String keyword) {
        List<Long> currentUserRelevanceModelIds = null;
        if (!SecurityUtils.currentUserIsAdmin()) {
            List<ModelPerson> modelPeople = remoteService.selectCurrentUserRelevanceModel();
            currentUserRelevanceModelIds = modelPeople.stream().map(ModelPerson::getModelId).collect(Collectors.toList());
        }

        Set<Long> modelIds = baseMapper.myPreselectDockingModelSelect(currentUserRelevanceModelIds);
        if (CollUtil.isEmpty(modelIds)) {
            return Collections.emptyList();
        }

        ModelListDTO modelListDTO = new ModelListDTO();
        modelListDTO.setId(modelIds);
        modelListDTO.setName(keyword);

        return remoteService.innerList(modelListDTO);
    }

    /**
     * 通过视频订单ID和rollbackId检查是否有选定的模特
     */
    @Override
    public boolean checkExistSelectedModelByVideoIdAndRollbackId(Long videoId, Long rollbackId) {
        return baseMapper.checkExistSelectedModelByVideoIdAndRollbackId(videoId, rollbackId);
    }

    /**
     * 运营修改订单信息时 淘汰原有意向模特
     */
    @Override
    public void eliminateModelByMatchId(Long matchId) {
        List<OrderVideoMatchPreselectModel> intentionModels = baseMapper.selectIntentionModelListByMatchId(matchId);
        if (CollUtil.isEmpty(intentionModels)) {
            return;
        }
        DateTime date = DateUtil.date();

        for (OrderVideoMatchPreselectModel intentionModel : intentionModels) {
            intentionModel.setStatus(PreselectStatusEnum.OUT.getCode());
            intentionModel.setRemark(OrderConstant.ORDER_PRESELECT_MODEL_INTENTION_MODEL_CHANGE);
            intentionModel.setOustType(PreselectModelOustTypeEnum.CUSTOMER_SERVICE_ELIMINATION.getCode());
            intentionModel.setOustTime(date);
            if (ModelIntentionEnum.MT_UN_CONFIRM.getCode().equals(intentionModel.getModelIntention())) {
                intentionModel.setModelIntention(ModelIntentionEnum.UN_CONFIRM.getCode());
            }
            intentionModel.setSelectStatus(OrderVideoModelSelectStatusEnum.CANCEL.getCode());
            intentionModel.setSelectTime(date);
        }
        baseMapper.updateBatchById(intentionModels);
    }

    @Override
    public EnglishStatisticsVO selectEnglishWorkbenchStatistics(EnglishStatisticsDTO dto) {

        EnglishStatisticsVO englishStatisticsVO = baseMapper.selectEnglishWorkbenchStatistics(dto);
        if (ObjectUtil.isNull(englishStatisticsVO)) {
            englishStatisticsVO = new EnglishStatisticsVO();
            englishStatisticsVO.setUnContactTotalCount(0);
            englishStatisticsVO.setUnContactCount(0);
            englishStatisticsVO.setContactingCount(0);
        } else {
            englishStatisticsVO.setUnContactCount(Optional.ofNullable(englishStatisticsVO.getUnContactCount()).orElse(0));
            englishStatisticsVO.setContactingCount(Optional.ofNullable(englishStatisticsVO.getContactingCount()).orElse(0));
            englishStatisticsVO.setUnContactTotalCount(englishStatisticsVO.getUnContactCount() + englishStatisticsVO.getContactingCount());
        }
        return englishStatisticsVO;
    }

    /**
     * 通过匹配单ID和模特ID查询未被淘汰的预选模特
     */
    @Override
    public OrderVideoMatchPreselectModel getActiveByMatchIdAndModelId(Long matchId, Long modelId) {
        return baseMapper.getActiveByMatchIdAndModelId(matchId, modelId);
    }

    /**
     * 视频订单回退淘汰预选模特
     */
    @Override
    public void rollbackOrderOustPreselectModel(Long matchId, String cause) {
        List<OrderVideoMatchPreselectModel> orderVideoMatchPreselectModels = baseMapper.selectActivePreselectModelListByMatchId(matchId);
        DateTime dateTime = DateUtil.date();
        for (OrderVideoMatchPreselectModel orderVideoMatchPreselectModel : orderVideoMatchPreselectModels) {
            orderVideoMatchPreselectModel.setStatus(PreselectStatusEnum.OUT.getCode());
            orderVideoMatchPreselectModel.setRemark(OrderConstant.ROLLBACK_ORDER + cause);
            orderVideoMatchPreselectModel.setOustType(PreselectModelOustTypeEnum.ROLLBACK_ORDER.getCode());
            orderVideoMatchPreselectModel.setOustTime(dateTime);
            orderVideoMatchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.CANCEL.getCode());
            if (PreselectModelAddTypeEnum.DISTRIBUTION.getCode().equals(orderVideoMatchPreselectModel.getAddType())
                    && DistributionResultEnum.PENDING.getCode().equals(orderVideoMatchPreselectModel.getDistributionResult())) {
                orderVideoMatchPreselectModel.setDistributionResult(DistributionResultEnum.CANCEL_DISTRIBUTION.getCode());
                orderVideoMatchPreselectModel.setDistributionResultCause(DistributionResultCauseEnum.ORDER_RETURN.getCode());
                orderVideoMatchPreselectModel.setDistributionResultTime(dateTime);
            }
        }
        baseMapper.updateBatchById(orderVideoMatchPreselectModels);
    }

    /**
     * 添加预选模特列表查询匹配单下非淘汰的模特
     */
    @Override
    public Set<Long> selectNormalPreselectModelByMatchId(Long matchId) {
        List<OrderVideoMatchPreselectModel> matchPreselectModels = baseMapper.selectActivePreselectModelListByMatchId(matchId);
        if (CollUtil.isEmpty(matchPreselectModels)) {
            return Collections.emptySet();
        }
        return matchPreselectModels.stream()
                .filter(item -> ObjectUtil.isNull(item.getDistributionResult())
                                || (
                                !DistributionResultEnum.CANCEL_DISTRIBUTION.getCode().equals(item.getDistributionResult())
                                        && !DistributionResultEnum.WANT_NOT.getCode().equals(item.getDistributionResult())
                        )
                )
                .map(OrderVideoMatchPreselectModel::getModelId).collect(Collectors.toSet());
    }

    /**
     * 暂停匹配单
     */
    @Override
    public void pauseMatch(Long matchId, String pauseReason) {
        List<OrderVideoMatchPreselectModel> matchPreselectModels = baseMapper.selectActivePreselectModelListByMatchId(matchId);
        if (CollUtil.isEmpty(matchPreselectModels)) {
            return;
        }
        DateTime dateTime = DateUtil.date();
        for (OrderVideoMatchPreselectModel matchPreselectModel : matchPreselectModels) {
            matchPreselectModel.setStatus(PreselectStatusEnum.OUT.getCode());
            matchPreselectModel.setRemark(String.format("暂停匹配-%s", pauseReason));
            matchPreselectModel.setOustType(PreselectModelOustTypeEnum.NOT_SELECTED.getCode());
            matchPreselectModel.setIsPauseOust(StatusTypeEnum.YES.getCode());
            matchPreselectModel.setOustTime(dateTime);
            matchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.CANCEL.getCode());
            matchPreselectModel.setSelectTime(dateTime);
            if (ModelIntentionEnum.MT_UN_CONFIRM.getCode().equals(matchPreselectModel.getModelIntention())) {
                matchPreselectModel.setModelIntention(ModelIntentionEnum.UN_CONFIRM.getCode());
            }
            if (PreselectModelAddTypeEnum.DISTRIBUTION.getCode().equals(matchPreselectModel.getAddType())
                    && DistributionResultEnum.PENDING.getCode().equals(matchPreselectModel.getDistributionResult())) {
                matchPreselectModel.setDistributionResult(DistributionResultEnum.CANCEL_DISTRIBUTION.getCode());
                matchPreselectModel.setDistributionResultCause(DistributionResultCauseEnum.ORDER_MATCHING_IS_TEMPORARILY_SUSPENDED.getCode());
                matchPreselectModel.setDistributionResultTime(dateTime);
            }
        }
        baseMapper.updateBatchById(matchPreselectModels);
    }

    /**
     * 设置模特选择记录的状态为卖方取消
     */
    @Override
    public void updateModelSelectStatusToCancelByVideoId(List<Long> machIds) {
        List<OrderVideoMatchPreselectModel> orderVideoMatchPreselectModels = baseMapper.selectList(new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .in(OrderVideoMatchPreselectModel::getMatchId, machIds)
                .ne(OrderVideoMatchPreselectModel::getSelectStatus, OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode())
        );

        for (OrderVideoMatchPreselectModel orderVideoMatchPreselectModel : orderVideoMatchPreselectModels) {
            if (OrderVideoModelSelectStatusEnum.IN_REVIEW.getCode().equals(orderVideoMatchPreselectModel.getSelectStatus())
                    || OrderVideoModelSelectStatusEnum.CONFIRM.getCode().equals(orderVideoMatchPreselectModel.getSelectStatus())) {
                orderVideoMatchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.CANCEL.getCode());
            }
            if (PreselectModelAddTypeEnum.DISTRIBUTION.getCode().equals(orderVideoMatchPreselectModel.getAddType())
                    && DistributionResultEnum.PENDING.getCode().equals(orderVideoMatchPreselectModel.getDistributionResult())) {
                orderVideoMatchPreselectModel.setDistributionResult(DistributionResultEnum.CANCEL_DISTRIBUTION.getCode());
                orderVideoMatchPreselectModel.setDistributionResultCause(DistributionResultCauseEnum.TRANSACTION_CLOSED.getCode());
                orderVideoMatchPreselectModel.setDistributionResultTime(DateUtil.date());
            }
        }

        baseMapper.updateBatchById(orderVideoMatchPreselectModels);
    }

    /**
     * 修改订单信息时将预选模特状态从非淘汰修改为未对接
     */
    @Override
    public void editOrderVideoPreselectModelFromJointedToUnJointed(Long matchId) {
        List<OrderVideoMatchPreselectModel> matchPreselectModels = baseMapper.selectActivePreselectModelListByMatchId(matchId);
        if (CollUtil.isEmpty(matchPreselectModels)) {
            return;
        }
        List<Long> modelIds = matchPreselectModels.stream().map(OrderVideoMatchPreselectModel::getModelId).collect(Collectors.toList());
        Map<Long, ModelOrderSimpleVO> modelSimpleMap = remoteService.getModelSimpleMap(modelIds);

        OrderVideoMatch orderVideoMatch = SpringUtils.getBean(OrderVideoMatchService.class).getById(matchId);
        OrderVideo orderVideo = SpringUtils.getBean(IOrderVideoService.class).getById(orderVideoMatch.getVideoId());

        for (OrderVideoMatchPreselectModel matchPreselectModel : matchPreselectModels) {
            ModelOrderSimpleVO modelOrderSimpleVO = modelSimpleMap.get(matchPreselectModel.getModelId());
            if (!modelOrderSimpleVO.getPlatform().contains(Convert.toStr(orderVideo.getPlatform()))
                    || !modelOrderSimpleVO.getNation().equals(orderVideo.getShootingCountry())
                    || (!modelOrderSimpleVO.getType().equals(orderVideo.getModelType()) && !ModelTypeEnum.ALL.getCode().equals(orderVideo.getModelType()))
            ) {
                matchPreselectModel.setStatus(PreselectStatusEnum.OUT.getCode());
                matchPreselectModel.setRemark(OrderConstant.ORDER_PRESELECT_MODEL_FAIL_TO_MEET_THE_CONDITION);
                matchPreselectModel.setOustType(PreselectModelOustTypeEnum.NOT_SELECTED.getCode());
                matchPreselectModel.setOustTime(DateUtil.date());
                if (ModelIntentionEnum.MT_UN_CONFIRM.getCode().equals(matchPreselectModel.getModelIntention())) {
                    matchPreselectModel.setModelIntention(ModelIntentionEnum.UN_CONFIRM.getCode());
                }
                matchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.CANCEL.getCode());
                matchPreselectModel.setSelectTime(DateUtil.date());
                continue;
            }
            matchPreselectModel.setStatus(PreselectStatusEnum.UN_JOINTED.getCode());
        }
        baseMapper.updateBatchById(matchPreselectModels);
    }

    /**
     * 通过匹配单ID查询预选模特记录
     */
    @Override
    public List<OrderVideoMatchPreselectModelVO> selectListByMatchId(Long matchId) {
        return getOrderVideoMatchPreselectModelVOS(baseMapper.selectListByMatchId(matchId));
    }

    /**
     * 提交预选模特 将其他非选定的预选模特置为已淘汰 同时设置模特快照数据
     */
    @Override
    public void submitPreselectModelUpdateToOut(Long matchId) {
        List<OrderVideoMatchPreselectModel> orderVideoMatchPreselectModels = baseMapper.selectList(new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .eq(OrderVideoMatchPreselectModel::getMatchId, matchId)
        );
        if (CollUtil.isEmpty(orderVideoMatchPreselectModels)) {
            return;
        }
        Set<Long> modelIds = orderVideoMatchPreselectModels.stream().map(OrderVideoMatchPreselectModel::getModelId).collect(Collectors.toSet());
        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(modelIds);

        DateTime date = DateUtil.date();
        for (OrderVideoMatchPreselectModel matchPreselectModel : orderVideoMatchPreselectModels) {
            ModelInfoVO modelInfoVO = modelMap.get(matchPreselectModel.getModelId());
            if (ObjectUtil.isNull(modelInfoVO)) {
                continue;
            }
            matchPreselectModel.setModelType(modelInfoVO.getType());
            matchPreselectModel.setModelPlatform(modelInfoVO.getPlatform());
            matchPreselectModel.setModelCooperation(modelInfoVO.getCooperation());
            matchPreselectModel.setModelCooperationScore(modelInfoVO.getCooperationScore());
            List<UserVO> persons = modelInfoVO.getPersons();
            if (CollUtil.isNotEmpty(persons)) {
                matchPreselectModel.setModelPersonId(persons.get(0).getId());
                matchPreselectModel.setModelPersonName(persons.get(0).getName());
            }

            if (PreselectStatusEnum.SELECTED.getCode().equals(matchPreselectModel.getStatus())
                    || PreselectStatusEnum.OUT.getCode().equals(matchPreselectModel.getStatus())
                    || OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode().equals(matchPreselectModel.getSelectStatus())) {
                continue;
            }
            matchPreselectModel.setStatus(PreselectStatusEnum.OUT.getCode());
            matchPreselectModel.setOustType(PreselectModelOustTypeEnum.NOT_SELECTED.getCode());
            matchPreselectModel.setOustTime(date);
            matchPreselectModel.setSelectTime(date);
            matchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.CANCEL.getCode());
            if (ModelIntentionEnum.MT_UN_CONFIRM.getCode().equals(matchPreselectModel.getModelIntention())) {
                matchPreselectModel.setModelIntention(ModelIntentionEnum.UN_CONFIRM.getCode());
            }
            if (PreselectModelAddTypeEnum.DISTRIBUTION.getCode().equals(matchPreselectModel.getAddType())
                    && DistributionResultEnum.PENDING.getCode().equals(matchPreselectModel.getDistributionResult())) {
                matchPreselectModel.setDistributionResult(DistributionResultEnum.CANCEL_DISTRIBUTION.getCode());
                matchPreselectModel.setDistributionResultCause(DistributionResultCauseEnum.UNCONFIRMED.getCode());
                matchPreselectModel.setDistributionResultTime(date);
            }
        }
        baseMapper.updateBatchById(orderVideoMatchPreselectModels);
    }

    /**
     * 设置预选模特为已选定
     */
    @Override
    public void selectedModel(MarkOrderDTO markOrderDTO) {
        OrderVideoMatchPreselectModel orderVideoMatchPreselectModel = baseMapper.selectById(markOrderDTO.getMatchPreselectModelId());
        Assert.notNull(orderVideoMatchPreselectModel, "预选模特不存在");
        Assert.isTrue(markOrderDTO.getId().equals(orderVideoMatchPreselectModel.getMatchId()), "预选模特与匹配单不一致");
        Assert.isTrue(PreselectStatusEnum.UN_JOINTED.getCode().equals(orderVideoMatchPreselectModel.getStatus()) || PreselectStatusEnum.JOINTED.getCode().equals(orderVideoMatchPreselectModel.getStatus()), "当前编辑模特已被淘汰，请刷新页面");
        Assert.isTrue(OrderVideoModelSelectStatusEnum.UN_HANDLE.getCode().equals(orderVideoMatchPreselectModel.getSelectStatus()) || OrderVideoModelSelectStatusEnum.IN_REVIEW.getCode().equals(orderVideoMatchPreselectModel.getSelectStatus()), "预选模特状态变更，请刷新后重试");
        //  2、校验模特档期
        boolean isSchedule = checkPreselectModelSchedule(List.of(orderVideoMatchPreselectModel));
        if (isSchedule) {
            if (ObjectUtil.isNotNull(markOrderDTO.getIsClear()) && StatusTypeEnum.YES.getCode().equals(markOrderDTO.getIsClear())) {
                modelCannotAcceptUpdateInfo(List.of(orderVideoMatchPreselectModel), null);
            }
            throw new CheckedException(2, orderVideoMatchPreselectModel.getRemark());
        }
        if (!SecurityUtils.currentUserIsAdmin()) {
            List<ModelPerson> modelPeople = remoteService.selectCurrentUserRelevanceModel();
            Assert.isTrue(modelPeople.stream().anyMatch(item -> item.getModelId().equals(orderVideoMatchPreselectModel.getModelId())), "仅可操作自己关联的模特");
        }

        if (ModelIntentionEnum.MT_UN_CONFIRM.getCode().equals(orderVideoMatchPreselectModel.getModelIntention())) {
            orderVideoMatchPreselectModel.setModelIntention(ModelIntentionEnum.UN_CONFIRM.getCode());
        }
        orderVideoMatchPreselectModel.setSelectedTime(DateUtil.date());
        orderVideoMatchPreselectModel.setStatus(PreselectStatusEnum.SELECTED.getCode());
        orderVideoMatchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.IN_REVIEW.getCode());
        orderVideoMatchPreselectModel.setSelectTime(DateUtil.date());
        if (CharSequenceUtil.isNotBlank(markOrderDTO.getShootAttention())
                && ObjectUtil.notEqual(orderVideoMatchPreselectModel.getShootAttention(), markOrderDTO.getShootAttention())
                && ObjectUtil.isNull(orderVideoMatchPreselectModel.getNeedRemindShootAttention())
        ) {
            orderVideoMatchPreselectModel.setNeedRemindShootAttention(StatusTypeEnum.YES.getCode());
        } else if (CharSequenceUtil.isBlank(markOrderDTO.getShootAttention())) {
            orderVideoMatchPreselectModel.setNeedRemindShootAttention(StatusTypeEnum.NO.getCode());
        }
        orderVideoMatchPreselectModel.setShootAttention(markOrderDTO.getShootAttention());
        if (CollUtil.isNotEmpty(markOrderDTO.getShootAttentionObjectKey())) {
            orderVideoMatchPreselectModel.setShootAttentionObjectKey(String.join(",", markOrderDTO.getShootAttentionObjectKey()));
        } else {
            orderVideoMatchPreselectModel.setShootAttentionObjectKey(null);
        }
        baseMapper.updateById(orderVideoMatchPreselectModel);
    }

    /**
     * 预选管理-模特匹配-我的预选-沟通中
     */
    @Override
    public List<OrderVideoMatchPreselectModelVO> selectMyPreselectDockingList(MyPreselectDockingListDTO dto) {
        List<OrderVideoMatchPreselectModel> matchPreselectModels = baseMapper.selectMyPreselectDockingList(dto);
        return getFilteringOut(getOrderVideoMatchPreselectModelVOS(matchPreselectModels));
    }

    /**
     * 查询当前匹配单活跃的预选模特
     */
    @Override
    public List<OrderVideoMatchPreselectModelVO> selectActivePreselectModelListByMatchId(Long matchId) {
        List<OrderVideoMatchPreselectModel> matchPreselectModels = baseMapper.selectActivePreselectModelListByMatchId(matchId);
        List<OrderVideoMatchPreselectModel> filteredModels = matchPreselectModels.stream()
                .filter(m -> !PreselectModelAddTypeEnum.DISTRIBUTION.getCode().equals(m.getAddType()) || ModelIntentionEnum.WANT.getCode().equals(m.getModelIntention()))
                .collect(Collectors.toList());
        return getFilteringOut(getOrderVideoMatchPreselectModelVOS(filteredModels));
    }

    /**
     * 查询当前匹配单活跃的预选模特（不填充数据）
     */
    @Override
    public List<OrderVideoMatchPreselectModel> selectActivePreselectModelListByMatchIdSimple(Long matchId) {
        return baseMapper.selectActivePreselectModelListByMatchId(matchId);
    }

    /**
     * 通过匹配单ID查询非淘汰的预选模特（订单列表）
     */
    @Override
    public List<OrderVideoMatchPreselectModel> selectActiveListByMatchIdsOfOrderVideoList(List<Long> matchIds) {
        return baseMapper.selectActivePreselectModelListByMatchIds(matchIds);
    }

    /**
     * 通过匹配单ID查询预选模特
     */
    @Override
    public List<OrderVideoMatchPreselectModelVO> selectListByMatchIds(List<Long> matchIds) {
        List<OrderVideoMatchPreselectModel> matchPreselectModels = baseMapper.selectListByMatchIds(matchIds);
        return getOrderVideoMatchPreselectModelVOS(matchPreselectModels);
    }

    /**
     * 组装预选模特列表
     */
    private void assembleOrderVideoMatchPreselectModelVOList(List<OrderVideoMatchPreselectModelVO> matchPreselectModelVOS) {
        if (CollUtil.isEmpty(matchPreselectModelVOS)) {
            return;
        }
        //  获取模特信息
        Set<Long> modelIds = matchPreselectModelVOS.stream().map(OrderVideoMatchPreselectModelVO::getModelId).collect(Collectors.toSet());
        List<AddPreselectModelListVO> modelListVOS = remoteService.selectModelInfoOfPreselection(ModelListDTO.builder().id(modelIds).build());
        Map<Long, AddPreselectModelListVO> modelListVOMap = modelListVOS.stream().collect(Collectors.toMap(AddPreselectModelListVO::getId, Function.identity()));


        List<ModelOrderVO> modelOrderCount = orderService.getModelOrderCount(modelIds);
        Map<Long, ModelOrderVO> modelOrderVOMap = modelOrderCount.stream().collect(Collectors.toMap(ModelOrderVO::getModelId, Function.identity()));
        for (OrderVideoMatchPreselectModelVO matchPreselectModelVO : matchPreselectModelVOS) {
            AddPreselectModelListVO addPreselectModelListVO = modelListVOMap.get(matchPreselectModelVO.getModelId());
            ModelOrderVO modelOrderVO = modelOrderVOMap.getOrDefault(matchPreselectModelVO.getModelId(), new ModelOrderVO());
            addPreselectModelListVO.setWaits(modelOrderVO.getWaits());
            addPreselectModelListVO.setToBeConfirm(modelOrderVO.getToBeConfirm());
            addPreselectModelListVO.setCan(addPreselectModelListVO.getAcceptability() - (modelOrderVO.getWaits() + modelOrderVO.getUnconfirmed()));
            addPreselectModelListVO.setCarryCount(modelOrderVO.getCarryCount());
            matchPreselectModelVO.setModel(addPreselectModelListVO);

            if (ObjectUtil.isNull(matchPreselectModelVO.getModelType())) {
                matchPreselectModelVO.setModelType(addPreselectModelListVO.getType());
                matchPreselectModelVO.setModelPlatform(addPreselectModelListVO.getPlatform());
                matchPreselectModelVO.setModelCooperation(addPreselectModelListVO.getCooperation());
                matchPreselectModelVO.setModelCooperationScore(addPreselectModelListVO.getCooperationScore());
                List<UserVO> persons = addPreselectModelListVO.getPersons();
                if (CollUtil.isNotEmpty(persons)) {
                    matchPreselectModelVO.setModelPersonName(persons.get(0).getName());
                }
            }
        }
    }

    /**
     * 查询选定模特列表通过匹配单ID
     */
    @Override
    public OrderVideoMatchPreselectModel getSelectedModelByMatchId(Long matchId) {
        return baseMapper.getSelectedModelByMatchId(matchId);
    }

    /**
     * 批量添加预选模特
     */
    @Override
    public void saveBatchOrderVideoMatchPreselectModel(List<SaveBatchOrderVideoMatchPreselectModelDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return;
        }
        List<OrderVideoMatchPreselectModel> orderVideoMatchPreselectModels = new ArrayList<>();

        Date date = dtoList.get(0).getAddTime();
        DateTime selectTimeout = DateUtil.offsetHour(date, orderVideoProperties.getPreselectModelOverTime());
        for (SaveBatchOrderVideoMatchPreselectModelDTO dto : dtoList) {
            OrderVideoMatchPreselectModel orderVideoMatchPreselectModel = new OrderVideoMatchPreselectModel();
            orderVideoMatchPreselectModel.setMatchId(dto.getMatchId());
            orderVideoMatchPreselectModel.setModelId(dto.getModelId());
            orderVideoMatchPreselectModel.setAddType(dto.getAddType().getCode());
            orderVideoMatchPreselectModel.setAddUserId(SecurityUtils.getUserId());
            orderVideoMatchPreselectModel.setAddTime(date);
            if (PreselectModelAddTypeEnum.MODEL_OPTIONAL.equals(dto.getAddType())) {
                orderVideoMatchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.IN_REVIEW.getCode());
                orderVideoMatchPreselectModel.setSelectTime(date);
            } else if (PreselectModelAddTypeEnum.INTENTION_MODEL.equals(dto.getAddType()) || PreselectModelAddTypeEnum.OPERATION.equals(dto.getAddType())) {
                orderVideoMatchPreselectModel.setSelectTimeout(selectTimeout);
            }

            orderVideoMatchPreselectModels.add(orderVideoMatchPreselectModel);
        }
        baseMapper.saveBatch(orderVideoMatchPreselectModels);

        // for (OrderVideoMatchPreselectModel orderVideoMatchPreselectModel : orderVideoMatchPreselectModels) {
        //     if (PreselectModelAddTypeEnum.INTENTION_MODEL.getCode().equals(orderVideoMatchPreselectModel.getAddType())) {
        //         delayQueueService.addTaskIfAbsent(DelayQueueConstant.ORDER_PRESELECT_MODEL_AUTO_OUT_QUEUE_NAME, orderVideoMatchPreselectModel.getId().toString(), orderVideoProperties.getPreselectModelOverTime(), TimeUnit.HOURS);
        //     }
        // }
    }

    /**
     * 查询匹配单已淘汰模特数量
     */
    @Override
    public Long countMatchOutModel(Long matchId) {
        return baseMapper.countMatchOutModel(matchId);
    }

    /**
     * 更新预选模特列表为已淘汰
     */
    @Override
    public void outPreselectModel(List<OutPreselectModelDTO> dtoList) {
        List<Long> modelIds = dtoList.stream().map(OutPreselectModelDTO::getModelId).collect(Collectors.toList());
        if (CollUtil.isEmpty(modelIds)) {
            return;
        }

        List<OrderVideoMatchPreselectModel> orderVideoMatchPreselectModels = baseMapper.selectListByModelIds(modelIds);
        if (CollUtil.isEmpty(orderVideoMatchPreselectModels)) {
            return;
        }

        Map<Long, OutPreselectModelDTO> dtoMap = dtoList.stream().collect(Collectors.toMap(OutPreselectModelDTO::getModelId, Function.identity()));
        DateTime dateTime = DateUtil.date();
        for (OrderVideoMatchPreselectModel orderVideoMatchPreselectModel : orderVideoMatchPreselectModels) {
            OutPreselectModelDTO outPreselectModelDTO = dtoMap.get(orderVideoMatchPreselectModel.getModelId());
            if (PreselectStatusEnum.UN_JOINTED.getCode().equals(orderVideoMatchPreselectModel.getStatus()) &&
                    !ModelStatusEnum.NORMAL.getCode().equals(outPreselectModelDTO.getModelStatus())) {
                orderVideoMatchPreselectModel.setStatus(PreselectStatusEnum.OUT.getCode());
                orderVideoMatchPreselectModel.setRemark(outPreselectModelDTO.getRemark());
                orderVideoMatchPreselectModel.setOustTime(dateTime);
            }
            if (PreselectModelAddTypeEnum.DISTRIBUTION.getCode().equals(orderVideoMatchPreselectModel.getAddType())
                    && DistributionResultEnum.PENDING.getCode().equals(orderVideoMatchPreselectModel.getDistributionResult())) {
                orderVideoMatchPreselectModel.setDistributionResult(DistributionResultEnum.CANCEL_DISTRIBUTION.getCode());
                orderVideoMatchPreselectModel.setDistributionResultTime(dateTime);
                if (ModelStatusEnum.JOURNEY.getCode().equals(outPreselectModelDTO.getModelStatus())) {
                    orderVideoMatchPreselectModel.setDistributionResultCause(DistributionResultCauseEnum.DURING_THE_MODEL_S_SCHEDULE.getCode());
                } else if (ModelStatusEnum.PAUSE.getCode().equals(outPreselectModelDTO.getModelStatus())) {
                    orderVideoMatchPreselectModel.setDistributionResultCause(DistributionResultCauseEnum.MODEL_SUSPENDS_COLLABORATION.getCode());
                } else if (ModelStatusEnum.CANCEL.getCode().equals(outPreselectModelDTO.getModelStatus())) {
                    orderVideoMatchPreselectModel.setDistributionResultCause(DistributionResultCauseEnum.MODEL_CANCELS_THE_COLLABORATION.getCode());
                }
            }
        }

        baseMapper.updateBatchById(orderVideoMatchPreselectModels);
    }

    /**
     * 订单列表-获取预选模特下拉框
     */
    @Override
    public List<ModelInfoVO> orderPreselectModelSelect(String keyword, List<Long> backUserIds) {
        Set<Long> preselectModelIds = baseMapper.getPreselectModelId();
        if (CollUtil.isEmpty(preselectModelIds)) {
            return Collections.emptyList();
        }

        ModelListDTO modelListDTO = new ModelListDTO();
        modelListDTO.setId(preselectModelIds);
        modelListDTO.setName(keyword);
        modelListDTO.setPersons(backUserIds);

        return remoteService.innerList(modelListDTO);
    }

    /**
     * 订单列表-获取预选添加人下拉框
     */
    @Override
    public Set<String> orderPreselectUserSelect(String keyword) {
        return baseMapper.getPreselectUserName(keyword);
    }

    /**
     * 通过视频订单id和模特id查询未对接或已对接的模特
     *
     * @param matchId 匹配单ID
     * @param modelId 模特id
     * @return 未对接的模特
     */
    @Override
    public OrderVideoMatchPreselectModel getUnJointedOrJointedByVideoIdAndModelId(Long matchId, Long modelId) {
        return baseMapper.getUnJointedOrJointedByVideoIdAndModelId(matchId, modelId);
    }

    /**
     * 更改预选模特状态
     */
    @Override
    public void editPreselectModel(EditPreselectModelDTO editPreselectModelDTO) {
        if (editPreselectModelDTO.getStatus().equals(PreselectStatusEnum.SELECTED.getCode())) {
            return;
        }

        OrderVideoMatchPreselectModel orderVideoMatchPreselectModel = baseMapper.selectById(editPreselectModelDTO.getId());
        Assert.notNull(orderVideoMatchPreselectModel, "预选模特不存在");
        Assert.isTrue(OrderVideoModelSelectStatusEnum.UN_HANDLE.getCode().equals(orderVideoMatchPreselectModel.getSelectStatus()) || OrderVideoModelSelectStatusEnum.IN_REVIEW.getCode().equals(orderVideoMatchPreselectModel.getSelectStatus()), "预选模特状态变更，请刷新后重试");
        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatchPreselectModel.getMatchId(), CacheConstants.ORDER_MATCH_LOCK_KEY_SECOND), "系统繁忙，请稍后重试~");

            if (!SecurityUtils.currentUserIsAdmin()) {
                List<ModelPerson> modelPeople = remoteService.selectCurrentUserRelevanceModel();
                Assert.isTrue(modelPeople.stream().anyMatch(item -> item.getModelId().equals(orderVideoMatchPreselectModel.getModelId())), "仅可操作自己关联的模特");
            }
            OrderVideoMatch match = SpringUtils.getBean(OrderVideoMatchService.class).getById(orderVideoMatchPreselectModel.getMatchId());
            OrderVideo orderVideo = SpringUtils.getBean(IOrderVideoService.class).getById(match.getVideoId());
            SpringUtils.getBean(IOrderVideoService.class).checkVideoStatus(orderVideo, OrderStatusEnum.UN_MATCH);


            // boolean isSchedule = checkPreselectModelSchedule(Collections.singletonList(orderVideoMatchPreselectModel));
            // if (isSchedule) {
            //     modelCannotAcceptUpdateInfo(Collections.singletonList(orderVideoMatchPreselectModel));
            //     throw new CheckedException("模特无法接单！");
            // }

            //  取消选定   清除标记的订单
            if (editPreselectModelDTO.getStatus().equals(PreselectStatusEnum.JOINTED.getCode())) {
                if (PreselectStatusEnum.SELECTED.getCode().equals(orderVideoMatchPreselectModel.getStatus())) {
                    SpringUtils.getBean(OrderVideoMatchService.class).clearFlag(orderVideoMatchPreselectModel.getMatchId(), null);
                }
                if (ModelIntentionEnum.UN_CONFIRM.getCode().equals(orderVideoMatchPreselectModel.getModelIntention())) {
                    orderVideoMatchPreselectModel.setModelIntention(ModelIntentionEnum.MT_UN_CONFIRM.getCode());
                    orderVideoMatchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.UN_HANDLE.getCode());
                }
                if (PreselectStatusEnum.UN_JOINTED.getCode().equals(orderVideoMatchPreselectModel.getStatus())){
                    orderVideoMatchPreselectModel.setSelectTimeout(null);
                    baseMapper.cleanTimeOutTime(editPreselectModelDTO.getId());
                }
            } else if (editPreselectModelDTO.getStatus().equals(PreselectStatusEnum.OUT.getCode())) {
                // OrderVideoMatch orderVideoMatch = SpringUtils.getBean(OrderVideoMatchService.class).getById(orderVideoMatchPreselectModel.getMatchId());
                // SpringUtils.getBean(IOrderVideoService.class).releaseOrderVideo(orderVideoMatch.getVideoId());

                orderVideoMatchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.CANCEL.getCode());
                orderVideoMatchPreselectModel.setOustType(PreselectModelOustTypeEnum.CUSTOMER_SERVICE_ELIMINATION.getCode());
                orderVideoMatchPreselectModel.setOustTime(DateUtil.date());
                orderVideoMatchPreselectModel.setSelectTime(DateUtil.date());
                if (ModelIntentionEnum.MT_UN_CONFIRM.getCode().equals(orderVideoMatchPreselectModel.getModelIntention())) {
                    orderVideoMatchPreselectModel.setModelIntention(ModelIntentionEnum.UN_CONFIRM.getCode());
                }
                if (CollUtil.isNotEmpty(editPreselectModelDTO.getObjectKeys())){
                    orderVideoMatchPreselectModel.setObjectKey(String.join(",", editPreselectModelDTO.getObjectKeys()));
                }
            }

            BeanUtil.copyProperties(editPreselectModelDTO, orderVideoMatchPreselectModel);
            baseMapper.updateById(orderVideoMatchPreselectModel);
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatchPreselectModel.getMatchId());
        }
    }

    /**
     * 添加预选模特
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AddDistributionErrorVO addPreselectModel(Integer isGund, Long matchId, List<Long> modelIds, PreselectModelAddTypeEnum addType, PreselectStatusEnum status, DateTime matchStartTime) {
        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(modelIds);
        OrderVideoMatch orderVideoMatch = SpringUtils.getBean(OrderVideoMatchService.class).getById(matchId);
        OrderVideo orderVideo = SpringUtils.getBean(IOrderVideoService.class).getById(orderVideoMatch.getVideoId());

        AddDistributionErrorVO addDistributionErrorVO = new AddDistributionErrorVO();
        List<Long> changeModelIds = checkModelConformOrderVideoInfoReturnModelId(modelMap, orderVideo);
        List<Long> addModelIds = new ArrayList<>();
        List<Long> intentionModelIds = new ArrayList<>();
        List<Long> generalProductLimitModels = checkGeneralProductLimitModels(isGund, orderVideo.getFirstMatchTime(), modelMap);
        for (OrderVideoMatchPreselectModel orderVideoMatchPreselectModel : baseMapper.checkPreselectModelIsExist(matchId, modelIds, addType)) {
            if (PreselectModelAddTypeEnum.INTENTION_MODEL.getCode().equals(orderVideoMatchPreselectModel.getAddType())
                    && !PreselectStatusEnum.OUT.getCode().equals(orderVideoMatchPreselectModel.getStatus())) {
                intentionModelIds.add(orderVideoMatchPreselectModel.getModelId());
            } else {
                addModelIds.add(orderVideoMatchPreselectModel.getModelId());
            }
        }

        if (CollUtil.isNotEmpty(changeModelIds)
                || CollUtil.isNotEmpty(addModelIds)
                || CollUtil.isNotEmpty(intentionModelIds)
                || CollUtil.isNotEmpty(generalProductLimitModels)
        ) {
            addDistributionErrorVO.setAddModelIds(addModelIds);
            addDistributionErrorVO.setIntentionModelIds(intentionModelIds);
            addDistributionErrorVO.setChangeModelIds(changeModelIds);
            addDistributionErrorVO.setGeneralProductLimitModelIds(generalProductLimitModels);
            return addDistributionErrorVO;
        }

        List<UserBlackModelVO> userBlackModelVOS = remoteService.userBlackModelListByBizUserId(orderVideo.getCreateOrderBizUserId());
        if (CollUtil.isNotEmpty(userBlackModelVOS)) {
            if (SecurityUtils.getLoginUserType() == UserTypeConstants.MANAGER_TYPE) {
                Assert.isFalse(CollUtil.containsAny(userBlackModelVOS.stream().map(UserBlackModelVO::getModelId).collect(Collectors.toSet()), modelIds), "选择的模特被拉黑，无法添加");
            } else if (SecurityUtils.getLoginUserType() == UserTypeConstants.MODEL_TYPE) {
                Assert.isFalse(CollUtil.containsAny(userBlackModelVOS.stream().map(UserBlackModelVO::getModelId).collect(Collectors.toSet()), modelIds), "Can not choose, please contact snail customer service to deal with ~");
            }
        }

        List<OrderVideoMatchPreselectModel> orderVideoMatchPreselectModels = new ArrayList<>();

        DateTime date = ObjectUtil.isNotNull(matchStartTime) ? matchStartTime : DateUtil.date();
        DateTime selectTimeout = DateUtil.offsetHour(date, orderVideoProperties.getPreselectModelOverTime());
        modelIds.forEach(modelId -> {
            OrderVideoMatchPreselectModel orderVideoMatchPreselectModel = new OrderVideoMatchPreselectModel();
            orderVideoMatchPreselectModel.setMatchId(matchId);
            orderVideoMatchPreselectModel.setAddType(addType.getCode());
            orderVideoMatchPreselectModel.setAddUserId(SecurityUtils.getUserId());
            orderVideoMatchPreselectModel.setAddTime(date);
            orderVideoMatchPreselectModel.setModelId(modelId);
            orderVideoMatchPreselectModel.setStatus(status.getCode());
            if (PreselectModelAddTypeEnum.MODEL_OPTIONAL.equals(addType)) {
                orderVideoMatchPreselectModel.setModelIntention(ModelIntentionEnum.WANT.getCode());
                orderVideoMatchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.IN_REVIEW.getCode());
                orderVideoMatchPreselectModel.setSelectTime(date);
            } else if (PreselectModelAddTypeEnum.INTENTION_MODEL.equals(addType) || PreselectModelAddTypeEnum.OPERATION.equals(addType)) {
                orderVideoMatchPreselectModel.setSelectTimeout(selectTimeout);

            }
            orderVideoMatchPreselectModels.add(orderVideoMatchPreselectModel);
        });
        baseMapper.saveBatch(orderVideoMatchPreselectModels);

        // for (OrderVideoMatchPreselectModel orderVideoMatchPreselectModel : orderVideoMatchPreselectModels) {
        //     if (PreselectModelAddTypeEnum.INTENTION_MODEL.equals(addType)) {
        //         delayQueueService.addTaskIfAbsent(DelayQueueConstant.ORDER_PRESELECT_MODEL_AUTO_OUT_QUEUE_NAME, orderVideoMatchPreselectModel.getId().toString(), orderVideoProperties.getPreselectModelOverTime(), TimeUnit.HOURS);
        //     }
        // }
        addDistributionErrorVO.setPreselectModelIds(orderVideoMatchPreselectModels.stream().map(OrderVideoMatchPreselectModel::getId).collect(Collectors.toList()));
        return addDistributionErrorVO;
    }

    /**
     * 校验通品非通品是否符合优质模特要求
     * @param isGund 是否通品
     * @param startTime 订单开始时间
     * @param modelMap 模特信息
     */
    private List<Long> checkGeneralProductLimitModels(Integer isGund, Date startTime, Map<Long, ModelInfoVO> modelMap) {
        ModelCooperationEnum requiredCooperationType = getOrderVideoAcceptModelCooperation(isGund, startTime);
        if (isGund == null || requiredCooperationType == null) {
            return List.of();
        }
        Integer requiredCode = requiredCooperationType.getCode();
        return modelMap.values().stream()
                .filter(model -> model.getCooperation() == null || !Objects.equals(model.getCooperation(), requiredCode))
                .map(ModelInfoVO::getId)
                .collect(Collectors.toList());

    }

    /**
     * 获取视频订单可接受的模特合作类型
     * @param isGund 是否通品
     * @param startTime 订单开始时间
     */
    private ModelCooperationEnum getOrderVideoAcceptModelCooperation(Integer isGund, Date startTime) {
        long daysElapsed = DateUtil.betweenDay(startTime, new Date(), true);
        if (daysElapsed <= BusinessConstants.COMMON_PRODUCT_QUALITY_MODEL_DAYS_THRESHOLD) {
            if (StatusTypeEnum.YES.getCode().equals(isGund)) {
                // 通品：3天内（第0、1、2天）仅可选择优质模特
                if (daysElapsed < BusinessConstants.COMMON_PRODUCT_QUALITY_MODEL_DAYS_THRESHOLD) {
                    return ModelCooperationEnum.QUALITY;
                }
            } else {
                // 非通品：1天内（当天，第0天）仅可选择优质模特
                if (daysElapsed < BusinessConstants.NON_COMMON_PRODUCT_QUALITY_MODEL_DAYS_THRESHOLD) {
                    return ModelCooperationEnum.QUALITY;
                }
            }
        }
        return null;
    }

    /**
     * 校验模特是否符合视频订单信息
     */
    @Override
    public void checkModelConformOrderVideoInfo(Map<Long, ModelInfoVO> modelMap, OrderVideo orderVideo) {
        for (Map.Entry<Long, ModelInfoVO> entry : modelMap.entrySet()) {
            ModelInfoVO modelInfoVO = entry.getValue();
            if (!modelInfoVO.getPlatform().contains(Convert.toStr(orderVideo.getPlatform()))
                    || !modelInfoVO.getNation().equals(orderVideo.getShootingCountry())
                    || (!modelInfoVO.getType().equals(orderVideo.getModelType()) && !ModelTypeEnum.ALL.getCode().equals(orderVideo.getModelType()))
            ) {
                throw new ServiceException(CharSequenceUtil.format("当前模特：{} 不满足订单要求", modelInfoVO.getName()));
            }
        }
    }

    /**
     * 校验模特是否符合视频订单信息
     *
     * @return 返回不满足条件的模特ID
     */
    public List<Long> checkModelConformOrderVideoInfoReturnModelId(Map<Long, ModelInfoVO> modelMap, OrderVideo orderVideo) {
        List<Long> modelIds = new ArrayList<>();

        for (Map.Entry<Long, ModelInfoVO> entry : modelMap.entrySet()) {
            ModelInfoVO modelInfoVO = entry.getValue();
            if (!modelInfoVO.getPlatform().contains(Convert.toStr(orderVideo.getPlatform()))
                    || !modelInfoVO.getNation().equals(orderVideo.getShootingCountry())
                    || (!modelInfoVO.getType().equals(orderVideo.getModelType()) && !ModelTypeEnum.ALL.getCode().equals(orderVideo.getModelType()))
            ) {
                modelIds.add(modelInfoVO.getId());
            }
        }
        return modelIds;
    }

    /**
     * 商家更换模特 对旧的选定的模特进行淘汰
     *
     * @param matchId 匹配单ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeModel(Long matchId, String reason) {
        OrderVideoMatchPreselectModel preselectModel = baseMapper.getSelectedModelByMatchId(matchId);
        Assert.notNull(preselectModel, "订单没有选定模特，无法继续！");

        preselectModel.setStatus(PreselectStatusEnum.OUT.getCode());
        preselectModel.setOustType(PreselectModelOustTypeEnum.MERCHANT_REJECTION.getCode());
        preselectModel.setOustTime(DateUtil.date());
        preselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.CANCEL.getCode());
        preselectModel.setRemark(reason);
        preselectModel.setSelectTime(DateUtil.date());
        updateById(preselectModel);
    }

    /**
     * 查询选定模特列表通过匹配单ID
     *
     * @return
     */
    @Override
    public List<OrderVideoMatchPreselectModel> selectedListByMatchIds(List<Long> matchIds) {
        return baseMapper.selectedListByMatchIds(matchIds);
    }

    /**
     * 校验预选模特状态
     *
     * @param orderVideoMatchPreselectModels 预选模特
     */
    private void checkPreselectModel(List<OrderVideoMatchPreselectModel> orderVideoMatchPreselectModels) {
        //  校验模特档期问题
        List<OrderVideoMatchPreselectModel> unCheckPreselectModel = orderVideoMatchPreselectModels.stream().filter(preselectModel -> preselectModel.getStatus().equals(PreselectStatusEnum.UN_JOINTED.getCode()) || preselectModel.getStatus().equals(PreselectStatusEnum.JOINTED.getCode())).collect(Collectors.toList());
        boolean isSchedule = this.checkPreselectModelSchedule(unCheckPreselectModel);
        if (isSchedule) {
            modelCannotAcceptUpdateInfo(unCheckPreselectModel, 1);
        }
        // else {
        //     //  判断是否超时未对接 如果是 设置为已淘汰
        //     List<OrderVideoMatchPreselectModel> overtimePreselectModel = new ArrayList<>();
        //     for (OrderVideoMatchPreselectModel preselectModel : orderVideoMatchPreselectModels) {
        //         if (PreselectModelAddTypeEnum.MODEL_OPTIONAL.getCode().equals(preselectModel.getAddType())
        //                 || !ModelIntentionEnum.MT_UN_CONFIRM.getCode().equals(preselectModel.getModelIntention())
        //                 || !PreselectStatusEnum.UN_JOINTED.getCode().equals(preselectModel.getStatus())
        //         ) {
        //             continue;
        //         }
        //         DateTime date = DateUtil.date();
        //         boolean in = DateUtil.isIn(date, preselectModel.getAddTime(), DateUtil.offset(preselectModel.getAddTime(), DateField.HOUR_OF_DAY, orderVideoProperties.getPreselectModelOverTime()));
        //         log.info("当前时间：{}，添加时间：{}，预选模特未对接超时时间：{}", date, DateUtil.format(preselectModel.getAddTime(), DatePattern.NORM_DATETIME_PATTERN), DateUtil.offset(preselectModel.getAddTime(), DateField.HOUR_OF_DAY, orderVideoProperties.getPreselectModelOverTime()));
        //         if (!in && DateUtil.compare(date, preselectModel.getAddTime()) > 0) {
        //             //  超过24小时
        //             preselectModel.setStatus(PreselectStatusEnum.OUT.getCode());
        //             preselectModel.setRemark(OrderConstant.ORDER_PRESELECT_MODEL_OVERTIME_REMARK);
        //             if (ModelIntentionEnum.MT_UN_CONFIRM.getCode().equals(preselectModel.getModelIntention())) {
        //                 preselectModel.setModelIntention(ModelIntentionEnum.TIMEOUT_NOT_SELECTED.getCode());
        //             }
        //             preselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.EXPIRE.getCode());
        //             preselectModel.setSelectTime(date);
        //             preselectModel.setOustType(PreselectModelOustTypeEnum.TIMEOUT_INTENTION_NOT_SELECTED.getCode());
        //             preselectModel.setOustTime(DateUtil.date());
        //             preselectModel.setProcessTime(date);
        //             // preselectModel.setProcessTime(date);
        //
        //             overtimePreselectModel.add(preselectModel);
        //         }
        //     }
        //     if (CollUtil.isNotEmpty(overtimePreselectModel)) {
        //         baseMapper.updateBatchById(overtimePreselectModel);
        //     }
        // }
    }

    /**
     * 校验模特档期问题
     *
     * @param orderVideoMatchPreselectModels 预选模特
     * @return 是否存在无档期
     */
    @Override
    public boolean checkPreselectModelSchedule(List<OrderVideoMatchPreselectModel> orderVideoMatchPreselectModels) {
        boolean flag = false;
        if (CollUtil.isEmpty(orderVideoMatchPreselectModels)) {
            return flag;
        }

        List<Long> modelIds = orderVideoMatchPreselectModels.stream().map(OrderVideoMatchPreselectModel::getModelId).collect(Collectors.toList());

        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMapWithBlackList(modelIds);

        //  获取模特接单数
        Map<Long, List<OrderVideoModel>> modelOrderMap = orderVideoModelService.getUnfinishedOrderModelByModelId(modelIds);

        //  获取逾期的模特id
        List<Long> overdueModelId = orderVideoModelService.checkModelOverdueVideo(modelIds);
        List<OrderVideoMatchPreselectModelVO> orderVideoMatchPreselectModelVOS = baseMapper.selectListVOByMatchIds(orderVideoMatchPreselectModels.stream()
                .map(OrderVideoMatchPreselectModel::getMatchId).collect(Collectors.toList()));
        Map<Long, OrderVideoMatchPreselectModelVO> preselectModelMap = orderVideoMatchPreselectModelVOS.stream().collect(Collectors.toMap(OrderVideoMatchPreselectModelVO::getId, p -> p));

        DateTime dateTime = DateUtil.date();
        for (OrderVideoMatchPreselectModel preselectModel : orderVideoMatchPreselectModels) {
            ModelInfoVO modelInfoVO = modelMap.get(preselectModel.getModelId());
            if (ObjectUtil.isNull(modelInfoVO)) {
                continue;
            }
            OrderVideoMatchPreselectModelVO orderVideoMatchPreselectModelVO = preselectModelMap.get(preselectModel.getId());
            if (CollUtil.isNotEmpty(modelInfoVO.getBlacklistUserIds())
                    && ObjectUtil.isNotNull(orderVideoMatchPreselectModelVO)
                    && modelInfoVO.getBlacklistUserIds().contains(orderVideoMatchPreselectModelVO.getBizUserId())) {
                flag = true;
                preselectModel.setStatus(PreselectStatusEnum.OUT.getCode());
                preselectModel.setRemark(OrderConstant.ORDER_PRESELECT_MODEL_BLACK_REMARK);
                preselectModel.setOustTime(dateTime);
                continue;
            }


            //  校验模特状态
            if (!modelInfoVO.getStatus().equals(ModelStatusEnum.NORMAL.getCode())) {
                flag = true;
                preselectModel.setStatus(PreselectStatusEnum.OUT.getCode());
                preselectModel.setOustTime(dateTime);
                if (modelInfoVO.getStatus().equals(ModelStatusEnum.PAUSE.getCode())) {
                    preselectModel.setRemark(OrderConstant.ORDER_PRESELECT_MODEL_PAUSE_REMARK);
                } else if (modelInfoVO.getStatus().equals(ModelStatusEnum.CANCEL.getCode())) {
                    preselectModel.setRemark(OrderConstant.ORDER_PRESELECT_MODEL_CANCEL_REMARK);
                } else if (modelInfoVO.getStatus().equals(ModelStatusEnum.JOURNEY.getCode())) {
                    preselectModel.setRemark(OrderConstant.ORDER_PRESELECT_MODEL_ON_THE_ROAD);
                }
                continue;
            }

            //  校验模特是否展示
            if (StatusTypeEnum.NO.getCode().equals(modelInfoVO.getIsShow())) {
                flag = true;
                preselectModel.setStatus(PreselectStatusEnum.OUT.getCode());
                preselectModel.setOustTime(dateTime);
                preselectModel.setRemark(OrderConstant.ORDER_PRESELECT_MODEL_NOT_SHOWN);
                continue;
            }

            //  校验模特可接单数
            List<OrderVideoModel> modelOrders = modelOrderMap.get(preselectModel.getModelId());
            if (CollUtil.isNotEmpty(modelOrders)) {
//                if (modelOrders.size() >= modelInfoVO.getAcceptability()) {
//                    flag = true;
//                    preselectModel.setStatus(PreselectStatusEnum.OUT.getCode());
//                    preselectModel.setOustTime(dateTime);
//                    preselectModel.setRemark(OrderConstant.ORDER_PRESELECT_MODEL_ORDER_LIMIT_REMARK);
//                    continue;
//                }

                //  校验模特是否有逾期订单
                if (overdueModelId.contains(preselectModel.getModelId())) {
                    flag = true;
                    preselectModel.setStatus(PreselectStatusEnum.OUT.getCode());
                    preselectModel.setOustTime(dateTime);
                    preselectModel.setRemark(OrderConstant.ORDER_PRESELECT_MODEL_OVERDUE_REMARK);
                    break;
                }
            }
        }

        return flag;
    }

    @Override
    public void modelCannotAcceptUpdateInfo(List<OrderVideoMatchPreselectModel> unCheckPreselectModel, Integer checkSelected) {
        //  释放视频订单到模特公池
        List<Long> outMatchIds = unCheckPreselectModel.stream().filter(item -> PreselectStatusEnum.OUT.getCode().equals(item.getStatus())).map(OrderVideoMatchPreselectModel::getMatchId).collect(Collectors.toList());
        if (CollUtil.isEmpty(outMatchIds)) {
            return;
        }
        // List<OrderVideoMatch> orderVideoMatches = SpringUtils.getBean(OrderVideoMatchService.class).listByIds(outMatchIds);
        // SpringUtils.getBean(IOrderVideoService.class).releaseOrderVideo(orderVideoMatches.stream().map(OrderVideoMatch::getVideoId).collect(Collectors.toList()));
        //  更新模特的选择记录
        for (OrderVideoMatchPreselectModel orderVideoMatchPreselectModel : unCheckPreselectModel) {
            if (!PreselectStatusEnum.OUT.getCode().equals(orderVideoMatchPreselectModel.getStatus())) {
                continue;
            }
            orderVideoMatchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.CANCEL.getCode());
            orderVideoMatchPreselectModel.setSelectTime(DateUtil.date());
        }
        baseMapper.updateBatchById(unCheckPreselectModel);

        //  清理标记匹配单相关字段
        SpringUtils.getBean(OrderVideoMatchService.class).clearFlag(outMatchIds, checkSelected);
    }

    private List<OrderVideoMatchPreselectModelVO> getOrderVideoMatchPreselectModelVOS(List<OrderVideoMatchPreselectModel> matchPreselectModels) {
        checkPreselectModel(matchPreselectModels);
        List<OrderVideoMatchPreselectModelVO> matchPreselectModelVOS = BeanUtil.copyToList(matchPreselectModels, OrderVideoMatchPreselectModelVO.class);
        assembleOrderVideoMatchPreselectModelVOList(matchPreselectModelVOS);
        return matchPreselectModelVOS;
    }

    /**
     * 过滤已淘汰模特
     */
    private List<OrderVideoMatchPreselectModelVO> getFilteringOut(List<OrderVideoMatchPreselectModelVO> orderVideoMatchPreselectModelVOS) {
        return orderVideoMatchPreselectModelVOS.stream().filter(item -> !PreselectStatusEnum.OUT.getCode().equals(item.getStatus())).collect(Collectors.toList());
    }

    /**
     * 清理不符合条件的预选模特数据
     * 用于定时任务自动清理
     *
     * @return 清理的记录数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanupInvalidPreselectModels() {
        log.info("开始执行预选模特清理任务");

        // 1. 查询所有未对接和已对接状态的预选模特
        List<OrderVideoMatchPreselectModel> activePreselectModels = baseMapper.selectList(
            new LambdaQueryWrapper<OrderVideoMatchPreselectModel>()
                .in(OrderVideoMatchPreselectModel::getStatus,
                    PreselectStatusEnum.UN_JOINTED.getCode(),
                    PreselectStatusEnum.JOINTED.getCode())
        );

        if (CollUtil.isEmpty(activePreselectModels)) {
            log.info("没有需要检查的预选模特数据");
            return 0;
        }

        log.info("找到 {} 条待检查的预选模特记录", activePreselectModels.size());

        // 2. 获取所有相关的模特ID
        Set<Long> modelIds = activePreselectModels.stream()
            .map(OrderVideoMatchPreselectModel::getModelId)
            .collect(Collectors.toSet());

        // 3. 查询模特信息（包含黑名单）
        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMapWithBlackList(modelIds);

        // 4. 查询逾期的模特ID
        List<Long> overdueModelIds = orderVideoModelService.checkModelOverdueVideo(modelIds);
        Set<Long> overdueModelIdSet = new HashSet<>(overdueModelIds);

        // 5. 获取预选模特的商家信息用于黑名单检查
        List<OrderVideoMatchPreselectModelVO> preselectModelVOs = baseMapper.selectListVOByMatchIds(
            activePreselectModels.stream()
                .map(OrderVideoMatchPreselectModel::getMatchId)
                .collect(Collectors.toList())
        );
        Map<Long, OrderVideoMatchPreselectModelVO> preselectModelVOMap = preselectModelVOs.stream()
            .collect(Collectors.toMap(OrderVideoMatchPreselectModelVO::getId, Function.identity()));

        // 6. 分类需要清理的模特
        List<OrderVideoMatchPreselectModel> toCleanupModels = new ArrayList<>();
        DateTime currentTime = DateUtil.date();

        for (OrderVideoMatchPreselectModel preselectModel : activePreselectModels) {
            ModelInfoVO modelInfo = modelMap.get(preselectModel.getModelId());
            OrderVideoMatchPreselectModelVO preselectModelVO = preselectModelVOMap.get(preselectModel.getId());

            String cleanupReason = null;
            Integer oustType = PreselectModelOustTypeEnum.CUSTOMER_SERVICE_ELIMINATION.getCode();

            // 检查模特是否存在
            if (ObjectUtil.isNull(modelInfo)) {
                cleanupReason = "模特信息不存在";
            }
            // 检查模特状态
            else if (!ModelStatusEnum.NORMAL.getCode().equals(modelInfo.getStatus())) {
                if (ModelStatusEnum.PAUSE.getCode().equals(modelInfo.getStatus())) {
                    cleanupReason = OrderConstant.ORDER_PRESELECT_MODEL_PAUSE_REMARK;
                } else if (ModelStatusEnum.CANCEL.getCode().equals(modelInfo.getStatus())) {
                    cleanupReason = OrderConstant.ORDER_PRESELECT_MODEL_CANCEL_REMARK;
                } else if (ModelStatusEnum.JOURNEY.getCode().equals(modelInfo.getStatus())) {
                    cleanupReason = OrderConstant.ORDER_PRESELECT_MODEL_ON_THE_ROAD;
                } else {
                    cleanupReason = "模特状态异常";
                }
            }
            // 检查模特展示状态
            else if (StatusTypeEnum.NO.getCode().equals(modelInfo.getIsShow())) {
                cleanupReason = OrderConstant.ORDER_PRESELECT_MODEL_NOT_SHOWN;
            }
            // 检查黑名单
            else if (CollUtil.isNotEmpty(modelInfo.getBlacklistUserIds())
                    && ObjectUtil.isNotNull(preselectModelVO)
                    && modelInfo.getBlacklistUserIds().contains(preselectModelVO.getBizUserId())) {
                cleanupReason = OrderConstant.ORDER_PRESELECT_MODEL_BLACK_REMARK;
            }
            // 检查逾期订单
            else if (overdueModelIdSet.contains(preselectModel.getModelId())) {
                cleanupReason = OrderConstant.ORDER_PRESELECT_MODEL_OVERDUE_REMARK;
            }

            if (CharSequenceUtil.isNotBlank(cleanupReason)) {
                preselectModel.setStatus(PreselectStatusEnum.OUT.getCode());
                preselectModel.setRemark(cleanupReason);
                preselectModel.setOustType(oustType);
                preselectModel.setOustTime(currentTime);
                preselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.CANCEL.getCode());
                preselectModel.setSelectTime(currentTime);

                // 处理分发结果
                if (PreselectModelAddTypeEnum.DISTRIBUTION.getCode().equals(preselectModel.getAddType())
                        && DistributionResultEnum.PENDING.getCode().equals(preselectModel.getDistributionResult())) {
                    preselectModel.setDistributionResult(DistributionResultEnum.CANCEL_DISTRIBUTION.getCode());
                    preselectModel.setDistributionResultCause(DistributionResultCauseEnum.CUSTOMER_SERVICE_CANCELLATION.getCode());
                    preselectModel.setDistributionResultTime(currentTime);
                }

                toCleanupModels.add(preselectModel);
            }
        }

        // 7. 批量更新需要清理的模特
        if (CollUtil.isNotEmpty(toCleanupModels)) {
            baseMapper.updateBatchById(toCleanupModels);

            // 8. 清理相关匹配单标记
            List<Long> outMatchIds = toCleanupModels.stream()
                .map(OrderVideoMatchPreselectModel::getMatchId)
                .distinct()
                .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(outMatchIds)) {
                SpringUtils.getBean(OrderVideoMatchService.class).clearFlag(outMatchIds, 1);
            }

            log.info("成功清理 {} 条不符合条件的预选模特记录", toCleanupModels.size());

            // 记录清理详情
            Map<String, Long> cleanupStats = toCleanupModels.stream()
                .collect(Collectors.groupingBy(OrderVideoMatchPreselectModel::getRemark, Collectors.counting()));
            log.info("清理统计: {}", cleanupStats);
        } else {
            log.info("没有需要清理的预选模特记录");
        }

        return toCleanupModels.size();
    }
}
